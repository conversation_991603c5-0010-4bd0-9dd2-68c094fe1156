import { motion } from 'framer-motion';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export const Loading: React.FC<LoadingProps> = ({ 
  size = 'md', 
  text = '加载中...' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-3">
      <motion.div
        className={`${sizeClasses[size]} border-4 border-white/20 border-t-white rounded-full`}
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      {text && (
        <motion.p
          className={`${textSizeClasses[size]} text-white/80 font-medium`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

// 骨架屏加载组件
export const WeatherCardSkeleton: React.FC = () => {
  return (
    <div className="weather-card p-6 animate-pulse">
      <div className="flex justify-between items-start mb-4">
        <div>
          <div className="h-6 bg-white/20 rounded w-24 mb-2"></div>
          <div className="h-4 bg-white/20 rounded w-16"></div>
        </div>
        <div className="h-12 w-12 bg-white/20 rounded-full"></div>
      </div>
      
      <div className="text-center mb-6">
        <div className="h-16 bg-white/20 rounded w-32 mx-auto mb-2"></div>
        <div className="h-4 bg-white/20 rounded w-20 mx-auto"></div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="text-center">
            <div className="h-4 bg-white/20 rounded w-12 mx-auto mb-1"></div>
            <div className="h-3 bg-white/20 rounded w-8 mx-auto"></div>
          </div>
        ))}
      </div>
    </div>
  );
};
