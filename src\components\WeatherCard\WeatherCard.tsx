import React from 'react';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  Thermometer, 
  Droplets, 
  Wind, 
  Eye, 
  Gauge,
  Sun,
  Moon,
  Heart,
  HeartOff
} from 'lucide-react';
import { CurrentWeather, City, AirQuality } from '../../types/weather';
import { WeatherIcon } from './WeatherIcon';
import { format } from 'date-fns';

interface WeatherCardProps {
  city: City;
  weather: CurrentWeather;
  airQuality?: AirQuality;
  isFavorite?: boolean;
  onToggleFavorite?: () => void;
  className?: string;
}

export const WeatherCard: React.FC<WeatherCardProps> = ({
  city,
  weather,
  airQuality,
  isFavorite = false,
  onToggleFavorite,
  className = ''
}) => {
  // 根据天气状况获取背景样式
  const getBackgroundClass = (iconCode: string) => {
    const code = parseInt(iconCode);
    if (code >= 100 && code <= 103) return 'sunny-bg';
    if (code >= 104 && code <= 213) return 'cloudy-bg';
    if (code >= 300 && code <= 399) return 'rainy-bg';
    if (code >= 400 && code <= 499) return 'snowy-bg';
    return 'gradient-bg';
  };

  // 获取AQI等级颜色
  const getAQIColor = (aqi: string) => {
    const aqiValue = parseInt(aqi);
    if (aqiValue <= 50) return 'text-green-400';
    if (aqiValue <= 100) return 'text-yellow-400';
    if (aqiValue <= 150) return 'text-orange-400';
    if (aqiValue <= 200) return 'text-red-400';
    if (aqiValue <= 300) return 'text-purple-400';
    return 'text-red-600';
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return format(date, 'HH:mm');
    } catch {
      return timeString;
    }
  };

  // 格式化日期
  const formatDate = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return format(date, 'MM月dd日');
    } catch {
      return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      className={`weather-card ${getBackgroundClass(weather.icon)} p-6 relative overflow-hidden ${className}`}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
      
      {/* 头部信息 */}
      <div className="relative z-10">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center space-x-2">
            <MapPin className="w-5 h-5 text-white/80" />
            <div>
              <h2 className="text-xl font-bold text-white">{city.name}</h2>
              <p className="text-white/70 text-sm">
                {city.adm1} {city.adm2 !== city.adm1 ? city.adm2 : ''}
              </p>
            </div>
          </div>
          
          {/* 收藏按钮 */}
          {onToggleFavorite && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onToggleFavorite}
              className="p-2 hover:bg-white/10 rounded-full transition-colors"
            >
              {isFavorite ? (
                <Heart className="w-5 h-5 text-red-400 fill-current" />
              ) : (
                <HeartOff className="w-5 h-5 text-white/60" />
              )}
            </motion.button>
          )}
        </div>

        {/* 主要天气信息 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <WeatherIcon 
              code={weather.icon} 
              size="lg"
              className="drop-shadow-lg"
            />
            <div>
              <div className="text-5xl font-light text-white mb-1">
                {weather.temp}°
              </div>
              <div className="text-white/80 text-lg">
                {weather.text}
              </div>
              <div className="text-white/60 text-sm">
                体感 {weather.feelsLike}°
              </div>
            </div>
          </div>
        </div>

        {/* 详细信息网格 */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <WeatherDetailItem
            icon={<Droplets className="w-4 h-4" />}
            label="湿度"
            value={`${weather.humidity}%`}
          />
          <WeatherDetailItem
            icon={<Wind className="w-4 h-4" />}
            label="风速"
            value={`${weather.windSpeed}km/h`}
            extra={weather.windDir}
          />
          <WeatherDetailItem
            icon={<Eye className="w-4 h-4" />}
            label="能见度"
            value={`${weather.vis}km`}
          />
          <WeatherDetailItem
            icon={<Gauge className="w-4 h-4" />}
            label="气压"
            value={`${weather.pressure}hPa`}
          />
        </div>

        {/* 空气质量 */}
        {airQuality && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="bg-white/10 rounded-lg p-3 mb-4"
          >
            <div className="flex justify-between items-center">
              <span className="text-white/80 text-sm">空气质量</span>
              <div className="flex items-center space-x-2">
                <span className={`font-bold ${getAQIColor(airQuality.aqi)}`}>
                  {airQuality.aqi}
                </span>
                <span className="text-white/60 text-sm">
                  {airQuality.category}
                </span>
              </div>
            </div>
          </motion.div>
        )}

        {/* 更新时间 */}
        <div className="flex justify-between items-center text-white/50 text-xs">
          <span>更新时间: {formatTime(weather.obsTime)}</span>
          <span>{formatDate(weather.obsTime)}</span>
        </div>
      </div>
    </motion.div>
  );
};

// 天气详情项组件
interface WeatherDetailItemProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  extra?: string;
}

const WeatherDetailItem: React.FC<WeatherDetailItemProps> = ({
  icon,
  label,
  value,
  extra
}) => {
  return (
    <div className="text-center">
      <div className="flex items-center justify-center text-white/60 mb-1">
        {icon}
        <span className="ml-1 text-xs">{label}</span>
      </div>
      <div className="text-white font-semibold">{value}</div>
      {extra && (
        <div className="text-white/60 text-xs">{extra}</div>
      )}
    </div>
  );
};
