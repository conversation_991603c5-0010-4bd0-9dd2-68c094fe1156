import React from 'react';
import { motion } from 'framer-motion';
import {
  Sun,
  Cloud,
  CloudRain,
  CloudSnow,
  CloudLightning,
  CloudDrizzle,
  Cloudy,
  Moon,
  CloudMoon,
  Zap,
  Wind,
  Eye
} from 'lucide-react';

interface WeatherIconProps {
  code: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  className?: string;
}

export const WeatherIcon: React.FC<WeatherIconProps> = ({
  code,
  size = 'md',
  animated = true,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  // 根据和风天气图标代码映射到对应的图标
  const getWeatherIcon = (iconCode: string) => {
    const code = parseInt(iconCode);
    
    // 晴天
    if (code === 100) return Sun;
    
    // 多云
    if (code >= 101 && code <= 103) return Cloudy;
    
    // 阴天
    if (code === 104) return Cloud;
    
    // 晴间多云（夜间）
    if (code >= 150 && code <= 153) return CloudMoon;
    
    // 阴天（夜间）
    if (code === 154) return Moon;
    
    // 雨天
    if (code >= 300 && code <= 399) {
      if (code >= 300 && code <= 303) return CloudDrizzle; // 阵雨
      if (code >= 304 && code <= 313) return CloudRain; // 雷阵雨
      return CloudRain; // 其他雨天
    }
    
    // 雪天
    if (code >= 400 && code <= 499) return CloudSnow;
    
    // 雾霾等
    if (code >= 500 && code <= 515) return Eye;
    
    // 沙尘暴等
    if (code >= 520 && code <= 531) return Wind;
    
    // 雷电
    if (code >= 302 && code <= 304) return CloudLightning;
    
    // 默认返回太阳图标
    return Sun;
  };

  const IconComponent = getWeatherIcon(code);
  
  // 动画配置
  const getAnimationProps = () => {
    if (!animated) return {};
    
    const codeNum = parseInt(code);
    
    // 太阳旋转动画
    if (codeNum === 100) {
      return {
        animate: { rotate: 360 },
        transition: { duration: 20, repeat: Infinity, ease: "linear" }
      };
    }
    
    // 云朵飘动动画
    if (codeNum >= 101 && codeNum <= 104) {
      return {
        animate: { x: [0, 5, 0] },
        transition: { duration: 3, repeat: Infinity, ease: "easeInOut" }
      };
    }
    
    // 雨滴动画
    if (codeNum >= 300 && codeNum <= 399) {
      return {
        animate: { y: [0, 2, 0] },
        transition: { duration: 1.5, repeat: Infinity, ease: "easeInOut" }
      };
    }
    
    // 雪花飘落动画
    if (codeNum >= 400 && codeNum <= 499) {
      return {
        animate: { 
          y: [0, 3, 0],
          rotate: [0, 10, -10, 0]
        },
        transition: { duration: 2, repeat: Infinity, ease: "easeInOut" }
      };
    }
    
    // 雷电闪烁动画
    if (codeNum >= 302 && codeNum <= 304) {
      return {
        animate: { opacity: [1, 0.5, 1] },
        transition: { duration: 0.5, repeat: Infinity, ease: "easeInOut" }
      };
    }
    
    return {};
  };

  // 根据天气类型获取颜色
  const getIconColor = () => {
    const codeNum = parseInt(code);
    
    if (codeNum === 100) return 'text-yellow-300'; // 晴天 - 黄色
    if (codeNum >= 101 && codeNum <= 104) return 'text-gray-300'; // 多云/阴天 - 灰色
    if (codeNum >= 150 && codeNum <= 154) return 'text-blue-200'; // 夜间 - 蓝色
    if (codeNum >= 300 && codeNum <= 399) return 'text-blue-400'; // 雨天 - 蓝色
    if (codeNum >= 400 && codeNum <= 499) return 'text-white'; // 雪天 - 白色
    if (codeNum >= 500 && codeNum <= 515) return 'text-gray-400'; // 雾霾 - 灰色
    if (codeNum >= 520 && codeNum <= 531) return 'text-yellow-600'; // 沙尘 - 黄褐色
    
    return 'text-white'; // 默认白色
  };

  return (
    <motion.div
      className={`${sizeClasses[size]} ${getIconColor()} ${className}`}
      {...getAnimationProps()}
    >
      <IconComponent className="w-full h-full drop-shadow-lg" />
    </motion.div>
  );
};

// 天气状况文字描述映射
export const getWeatherDescription = (code: string): string => {
  const codeNum = parseInt(code);
  
  const descriptions: { [key: number]: string } = {
    100: '晴',
    101: '多云',
    102: '少云',
    103: '晴间多云',
    104: '阴',
    150: '晴',
    151: '多云',
    152: '少云',
    153: '晴间多云',
    154: '阴',
    300: '阵雨',
    301: '强阵雨',
    302: '雷阵雨',
    303: '强雷阵雨',
    304: '雷阵雨伴有冰雹',
    305: '小雨',
    306: '中雨',
    307: '大雨',
    308: '极端降雨',
    309: '毛毛雨',
    310: '暴雨',
    311: '大暴雨',
    312: '特大暴雨',
    313: '冻雨',
    314: '小到中雨',
    315: '中到大雨',
    316: '大到暴雨',
    317: '暴雨到大暴雨',
    318: '大暴雨到特大暴雨',
    399: '雨',
    400: '小雪',
    401: '中雪',
    402: '大雪',
    403: '暴雪',
    404: '雨夹雪',
    405: '雨雪天气',
    406: '阵雨夹雪',
    407: '阵雪',
    408: '小到中雪',
    409: '中到大雪',
    410: '大到暴雪',
    499: '雪',
    500: '薄雾',
    501: '雾',
    502: '霾',
    503: '扬沙',
    504: '浮尘',
    507: '沙尘暴',
    508: '强沙尘暴',
    509: '浓雾',
    510: '强浓雾',
    511: '中度霾',
    512: '重度霾',
    513: '严重霾',
    514: '大雾',
    515: '特强浓雾'
  };
  
  return descriptions[codeNum] || '未知';
};
