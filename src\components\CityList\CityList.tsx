import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Trash2, MapPin } from 'lucide-react';
import { City, CurrentWeather } from '../../types/weather';
import { WeatherIcon } from '../WeatherCard/WeatherIcon';

interface CityWeatherData {
  city: City;
  weather: CurrentWeather;
}

interface CityListProps {
  cities: CityWeatherData[];
  onCitySelect: (city: City) => void;
  onCityRemove?: (cityId: string) => void;
  onAddCity?: () => void;
  selectedCityId?: string;
  className?: string;
}

export const CityList: React.FC<CityListProps> = ({
  cities,
  onCitySelect,
  onCityRemove,
  onAddCity,
  selectedCityId,
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {/* 添加城市按钮 */}
      {onAddCity && (
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={onAddCity}
          className="w-full glass-effect rounded-xl p-4 flex items-center justify-center 
                     space-x-2 text-white/80 hover:text-white hover:bg-white/10 
                     transition-all duration-200 border-2 border-dashed border-white/30"
        >
          <Plus className="w-5 h-5" />
          <span className="font-medium">添加城市</span>
        </motion.button>
      )}

      {/* 城市列表 */}
      <AnimatePresence>
        {cities.map((cityData, index) => (
          <CityListItem
            key={cityData.city.id}
            cityData={cityData}
            isSelected={selectedCityId === cityData.city.id}
            onSelect={() => onCitySelect(cityData.city)}
            onRemove={onCityRemove ? () => onCityRemove(cityData.city.id) : undefined}
            index={index}
          />
        ))}
      </AnimatePresence>

      {/* 空状态 */}
      {cities.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-8 text-white/60"
        >
          <MapPin className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="text-lg font-medium mb-1">暂无收藏城市</p>
          <p className="text-sm">点击上方按钮添加您关注的城市</p>
        </motion.div>
      )}
    </div>
  );
};

// 城市列表项组件
interface CityListItemProps {
  cityData: CityWeatherData;
  isSelected: boolean;
  onSelect: () => void;
  onRemove?: () => void;
  index: number;
}

const CityListItem: React.FC<CityListItemProps> = ({
  cityData,
  isSelected,
  onSelect,
  onRemove,
  index
}) => {
  const { city, weather } = cityData;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ delay: index * 0.1 }}
      className={`glass-effect rounded-xl p-4 cursor-pointer transition-all duration-200 
                  ${isSelected 
                    ? 'ring-2 ring-white/40 bg-white/20' 
                    : 'hover:bg-white/10'
                  }`}
      onClick={onSelect}
    >
      <div className="flex items-center justify-between">
        {/* 城市信息 */}
        <div className="flex items-center space-x-3 flex-1">
          <WeatherIcon 
            code={weather.icon} 
            size="md"
            className="flex-shrink-0"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-white font-semibold truncate">
                {city.name}
              </h3>
              {isSelected && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="w-2 h-2 bg-blue-400 rounded-full"
                />
              )}
            </div>
            <p className="text-white/60 text-sm truncate">
              {city.adm1} {city.adm2 !== city.adm1 ? city.adm2 : ''}
            </p>
          </div>
        </div>

        {/* 天气信息 */}
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-white font-bold text-lg">
              {weather.temp}°
            </div>
            <div className="text-white/60 text-xs">
              {weather.text}
            </div>
          </div>

          {/* 删除按钮 */}
          {onRemove && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className="p-2 hover:bg-red-500/20 rounded-lg transition-colors 
                         text-white/60 hover:text-red-400"
              title="移除城市"
            >
              <Trash2 className="w-4 h-4" />
            </motion.button>
          )}
        </div>
      </div>

      {/* 额外天气信息 */}
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{ 
          height: isSelected ? 'auto' : 0, 
          opacity: isSelected ? 1 : 0 
        }}
        transition={{ duration: 0.2 }}
        className="overflow-hidden"
      >
        {isSelected && (
          <div className="mt-3 pt-3 border-t border-white/20">
            <div className="grid grid-cols-3 gap-3 text-center">
              <div>
                <div className="text-white/60 text-xs">体感</div>
                <div className="text-white text-sm font-medium">
                  {weather.feelsLike}°
                </div>
              </div>
              <div>
                <div className="text-white/60 text-xs">湿度</div>
                <div className="text-white text-sm font-medium">
                  {weather.humidity}%
                </div>
              </div>
              <div>
                <div className="text-white/60 text-xs">风速</div>
                <div className="text-white text-sm font-medium">
                  {weather.windSpeed}km/h
                </div>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};
