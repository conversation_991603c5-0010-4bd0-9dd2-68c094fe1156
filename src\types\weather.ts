// 城市信息类型
export interface City {
  id: string;
  name: string;
  country: string;
  adm1: string; // 省/州
  adm2: string; // 市
  lat: string;
  lon: string;
  tz: string; // 时区
  utcOffset: string;
  isDst: string;
  type: string;
  rank: string;
  fxLink: string;
}

// 实时天气数据类型
export interface CurrentWeather {
  obsTime: string; // 观测时间
  temp: string; // 温度
  feelsLike: string; // 体感温度
  icon: string; // 天气图标代码
  text: string; // 天气描述
  wind360: string; // 风向角度
  windDir: string; // 风向
  windScale: string; // 风力等级
  windSpeed: string; // 风速
  humidity: string; // 湿度
  precip: string; // 降水量
  pressure: string; // 气压
  vis: string; // 能见度
  cloud: string; // 云量
  dew: string; // 露点温度
}

// 天气预报数据类型
export interface DailyForecast {
  fxDate: string; // 预报日期
  sunrise: string; // 日出时间
  sunset: string; // 日落时间
  moonrise: string; // 月升时间
  moonset: string; // 月落时间
  moonPhase: string; // 月相
  moonPhaseIcon: string; // 月相图标
  tempMax: string; // 最高温度
  tempMin: string; // 最低温度
  iconDay: string; // 白天天气图标
  textDay: string; // 白天天气描述
  iconNight: string; // 夜间天气图标
  textNight: string; // 夜间天气描述
  wind360Day: string; // 白天风向角度
  windDirDay: string; // 白天风向
  windScaleDay: string; // 白天风力等级
  windSpeedDay: string; // 白天风速
  wind360Night: string; // 夜间风向角度
  windDirNight: string; // 夜间风向
  windScaleNight: string; // 夜间风力等级
  windSpeedNight: string; // 夜间风速
  humidity: string; // 湿度
  precip: string; // 降水量
  pressure: string; // 气压
  vis: string; // 能见度
  cloud: string; // 云量
  uvIndex: string; // 紫外线指数
}

// 空气质量数据类型
export interface AirQuality {
  pubTime: string; // 发布时间
  aqi: string; // AQI指数
  level: string; // AQI等级
  category: string; // AQI类别
  primary: string; // 主要污染物
  pm10: string; // PM10
  pm2p5: string; // PM2.5
  no2: string; // 二氧化氮
  so2: string; // 二氧化硫
  co: string; // 一氧化碳
  o3: string; // 臭氧
}

// API响应基础类型
export interface ApiResponse<T> {
  code: string;
  updateTime: string;
  fxLink: string;
  data?: T;
}

// 城市搜索响应类型
export interface CitySearchResponse extends ApiResponse<City[]> {
  location: City[];
}

// 实时天气响应类型
export interface CurrentWeatherResponse extends ApiResponse<CurrentWeather> {
  now: CurrentWeather;
}

// 天气预报响应类型
export interface DailyForecastResponse extends ApiResponse<DailyForecast[]> {
  daily: DailyForecast[];
}

// 空气质量响应类型
export interface AirQualityResponse extends ApiResponse<AirQuality> {
  now: AirQuality;
}

// 天气状况枚举
export enum WeatherCondition {
  SUNNY = 'sunny',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
  THUNDERSTORM = 'thunderstorm',
}

// 收藏城市类型
export interface FavoriteCity {
  id: string;
  name: string;
  adm1: string;
  adm2: string;
  lat: string;
  lon: string;
  addedAt: string;
}
