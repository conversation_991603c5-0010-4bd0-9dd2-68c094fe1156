import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { SearchBox } from './components/SearchBox/SearchBox';
import { WeatherCard } from './components/WeatherCard/WeatherCard';
import { CityList } from './components/CityList/CityList';
import { Loading, WeatherCardSkeleton } from './components/common/Loading';
import { ErrorBoundary, ApiError } from './components/common/ErrorBoundary';
import { City, CurrentWeather, AirQuality } from './types/weather';

// 模拟数据
const mockCity: City = {
  id: '101010100',
  name: '北京',
  country: '中国',
  adm1: '北京',
  adm2: '北京',
  lat: '39.90',
  lon: '116.41',
  tz: 'Asia/Shanghai',
  utcOffset: '+08:00',
  isDst: '0',
  type: 'city',
  rank: '10',
  fxLink: 'https://www.qweather.com/weather/beijing-101010100.html'
};

const mockWeather: CurrentWeather = {
  obsTime: new Date().toISOString(),
  temp: '22',
  feelsLike: '25',
  icon: '100',
  text: '晴',
  wind360: '270',
  windDir: '西风',
  windScale: '3',
  windSpeed: '15',
  humidity: '45',
  precip: '0.0',
  pressure: '1013',
  vis: '10',
  cloud: '10',
  dew: '8'
};

const mockAirQuality: AirQuality = {
  pubTime: new Date().toISOString(),
  aqi: '85',
  level: '2',
  category: '良',
  primary: 'PM2.5',
  pm10: '45',
  pm2p5: '32',
  no2: '28',
  so2: '8',
  co: '0.6',
  o3: '120'
};

function App() {
  const [selectedCity, setSelectedCity] = useState<City>(mockCity);
  const [isLoading, setIsLoading] = useState(false);
  const [favoriteCities] = useState([
    { city: mockCity, weather: mockWeather }
  ]);

  const handleCitySelect = (city: City) => {
    setSelectedCity(city);
    console.log('Selected city:', city);
  };

  const handleLocationRequest = () => {
    console.log('Location requested');
    // 这里将实现地理定位功能
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen gradient-bg">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-pink-600/20" />
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http://www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50" />

        <div className="relative z-10 container mx-auto px-4 py-8">
          {/* 头部 */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold text-white mb-2">
              天气查询
            </h1>
            <p className="text-white/70 text-lg">
              实时天气信息，精准预报服务
            </p>
          </motion.div>

          {/* 搜索框 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <SearchBox
              onCitySelect={handleCitySelect}
              onLocationRequest={handleLocationRequest}
              isLoading={isLoading}
            />
          </motion.div>

          {/* 主要内容区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 主天气卡片 */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              {isLoading ? (
                <WeatherCardSkeleton />
              ) : (
                <WeatherCard
                  city={selectedCity}
                  weather={mockWeather}
                  airQuality={mockAirQuality}
                  isFavorite={true}
                  onToggleFavorite={() => console.log('Toggle favorite')}
                />
              )}
            </motion.div>

            {/* 侧边栏 - 收藏城市列表 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-6"
            >
              <div>
                <h2 className="text-xl font-semibold text-white mb-4">
                  收藏城市
                </h2>
                <CityList
                  cities={favoriteCities}
                  onCitySelect={handleCitySelect}
                  onCityRemove={(cityId) => console.log('Remove city:', cityId)}
                  onAddCity={() => console.log('Add city')}
                  selectedCityId={selectedCity.id}
                />
              </div>
            </motion.div>
          </div>

          {/* 底部信息 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-center mt-12 text-white/50 text-sm"
          >
            <p>数据来源：和风天气 | 更新时间：{new Date().toLocaleTimeString()}</p>
          </motion.div>
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default App;
