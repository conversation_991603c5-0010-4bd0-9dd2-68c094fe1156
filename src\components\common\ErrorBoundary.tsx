import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; retry?: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error} retry={this.retry} />;
    }

    return this.props.children;
  }
}

// 默认错误回退组件
const DefaultErrorFallback: React.FC<{ error?: Error; retry?: () => void }> = ({ 
  error, 
  retry 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center p-8 text-center"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.1 }}
        className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4"
      >
        <AlertCircle className="w-8 h-8 text-red-400" />
      </motion.div>
      
      <h3 className="text-xl font-semibold text-white mb-2">
        出现了一些问题
      </h3>
      
      <p className="text-white/70 mb-6 max-w-md">
        {error?.message || '应用遇到了意外错误，请稍后重试'}
      </p>
      
      {retry && (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={retry}
          className="flex items-center space-x-2 px-6 py-3 bg-white/10 hover:bg-white/20 
                     rounded-lg border border-white/20 text-white font-medium 
                     transition-colors duration-200"
        >
          <RefreshCw className="w-4 h-4" />
          <span>重试</span>
        </motion.button>
      )}
    </motion.div>
  );
};

// API错误显示组件
interface ApiErrorProps {
  message?: string;
  onRetry?: () => void;
  className?: string;
}

export const ApiError: React.FC<ApiErrorProps> = ({ 
  message = '数据加载失败', 
  onRetry,
  className = ''
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`flex flex-col items-center justify-center p-6 ${className}`}
    >
      <AlertCircle className="w-12 h-12 text-red-400 mb-3" />
      <p className="text-white/80 text-center mb-4">{message}</p>
      {onRetry && (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onRetry}
          className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 
                     rounded-lg border border-white/20 text-white text-sm font-medium 
                     transition-colors duration-200"
        >
          <RefreshCw className="w-3 h-3" />
          <span>重试</span>
        </motion.button>
      )}
    </motion.div>
  );
};
