import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, MapPin, X, Clock } from 'lucide-react';
import { City } from '../../types/weather';

interface SearchBoxProps {
  onCitySelect: (city: City) => void;
  onLocationRequest?: () => void;
  isLoading?: boolean;
  placeholder?: string;
  className?: string;
}

interface SearchResult extends City {
  matchType?: 'name' | 'pinyin' | 'code';
}

export const SearchBox: React.FC<SearchBoxProps> = ({
  onCitySelect,
  onLocationRequest,
  isLoading = false,
  placeholder = '搜索城市...',
  className = ''
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<City[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 模拟搜索结果（实际项目中会调用API）
  const mockSearchResults: SearchResult[] = [
    {
      id: '101010100',
      name: '北京',
      country: '中国',
      adm1: '北京',
      adm2: '北京',
      lat: '39.90',
      lon: '116.41',
      tz: 'Asia/Shanghai',
      utcOffset: '+08:00',
      isDst: '0',
      type: 'city',
      rank: '10',
      fxLink: 'https://www.qweather.com/weather/beijing-101010100.html',
      matchType: 'name'
    },
    {
      id: '101020100',
      name: '上海',
      country: '中国',
      adm1: '上海',
      adm2: '上海',
      lat: '31.23',
      lon: '121.47',
      tz: 'Asia/Shanghai',
      utcOffset: '+08:00',
      isDst: '0',
      type: 'city',
      rank: '10',
      fxLink: 'https://www.qweather.com/weather/shanghai-101020100.html',
      matchType: 'name'
    }
  ];

  // 处理搜索
  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsSearching(true);
    
    // 模拟API调用延迟
    setTimeout(() => {
      const filteredResults = mockSearchResults.filter(city =>
        city.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        city.adm1.toLowerCase().includes(searchQuery.toLowerCase()) ||
        city.adm2.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setResults(filteredResults);
      setIsSearching(false);
    }, 300);
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    handleSearch(value);
  };

  // 处理城市选择
  const handleCitySelect = (city: City) => {
    setQuery(city.name);
    setIsOpen(false);
    onCitySelect(city);
    
    // 添加到最近搜索
    const updatedRecent = [city, ...recentSearches.filter(c => c.id !== city.id)].slice(0, 5);
    setRecentSearches(updatedRecent);
    localStorage.setItem('recentSearches', JSON.stringify(updatedRecent));
  };

  // 清空搜索
  const handleClear = () => {
    setQuery('');
    setResults([]);
    inputRef.current?.focus();
  };

  // 处理定位请求
  const handleLocationRequest = () => {
    if (onLocationRequest) {
      onLocationRequest();
      setIsOpen(false);
    }
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 加载最近搜索
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to parse recent searches:', error);
      }
    }
  }, []);

  const showResults = isOpen && (results.length > 0 || recentSearches.length > 0 || query.length > 0);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* 搜索输入框 */}
      <div className="relative">
        <motion.div
          className="glass-effect rounded-2xl p-4 flex items-center space-x-3"
          whileFocus={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <Search className={`w-5 h-5 ${isSearching ? 'animate-pulse' : ''} text-white/60`} />
          
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={() => setIsOpen(true)}
            placeholder={placeholder}
            disabled={isLoading}
            className="flex-1 bg-transparent text-white placeholder-white/50 
                       outline-none text-lg font-medium"
          />
          
          {query && (
            <motion.button
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              onClick={handleClear}
              className="p-1 hover:bg-white/10 rounded-full transition-colors"
            >
              <X className="w-4 h-4 text-white/60" />
            </motion.button>
          )}
          
          {onLocationRequest && (
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleLocationRequest}
              className="p-2 hover:bg-white/10 rounded-full transition-colors"
              title="获取当前位置"
            >
              <MapPin className="w-5 h-5 text-white/80" />
            </motion.button>
          )}
        </motion.div>
      </div>

      {/* 搜索结果下拉框 */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 glass-effect rounded-2xl 
                       border border-white/20 shadow-xl z-50 max-h-80 overflow-y-auto"
          >
            {/* 最近搜索 */}
            {query.length === 0 && recentSearches.length > 0 && (
              <div className="p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Clock className="w-4 h-4 text-white/60" />
                  <span className="text-sm text-white/60 font-medium">最近搜索</span>
                </div>
                {recentSearches.map((city) => (
                  <SearchResultItem
                    key={city.id}
                    city={city}
                    onClick={() => handleCitySelect(city)}
                  />
                ))}
              </div>
            )}

            {/* 搜索结果 */}
            {results.length > 0 && (
              <div className="p-4">
                {query.length > 0 && (
                  <div className="text-sm text-white/60 font-medium mb-3">
                    搜索结果
                  </div>
                )}
                {results.map((city) => (
                  <SearchResultItem
                    key={city.id}
                    city={city}
                    onClick={() => handleCitySelect(city)}
                    matchType={city.matchType}
                  />
                ))}
              </div>
            )}

            {/* 无结果 */}
            {query.length > 0 && results.length === 0 && !isSearching && (
              <div className="p-6 text-center text-white/60">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>未找到相关城市</p>
                <p className="text-sm mt-1">请尝试其他关键词</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// 搜索结果项组件
interface SearchResultItemProps {
  city: City;
  onClick: () => void;
  matchType?: string;
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({ 
  city, 
  onClick, 
  matchType 
}) => {
  return (
    <motion.div
      whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
      onClick={onClick}
      className="flex items-center justify-between p-3 rounded-lg cursor-pointer 
                 transition-colors duration-200"
    >
      <div className="flex items-center space-x-3">
        <MapPin className="w-4 h-4 text-white/60" />
        <div>
          <div className="text-white font-medium">{city.name}</div>
          <div className="text-white/60 text-sm">
            {city.adm1} {city.adm2 !== city.adm1 ? city.adm2 : ''}
          </div>
        </div>
      </div>
      
      {matchType && (
        <div className="text-xs text-white/40 bg-white/10 px-2 py-1 rounded">
          {matchType === 'pinyin' ? '拼音' : matchType === 'code' ? '代码' : '名称'}
        </div>
      )}
    </motion.div>
  );
};
